<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lớp Học - <PERSON>thon</title>
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="../assets/images/favicon.png">
    <link rel="shortcut icon" type="image/png" href="../assets/images/favicon.png">
    <link rel="apple-touch-icon" href="../assets/images/favicon.png">
    <link rel="apple-touch-icon" sizes="72x72" href="../assets/images/favicon.png">
    <link rel="apple-touch-icon" sizes="114x114" href="../assets/images/favicon.png">


    <link rel="stylesheet" href="../assets/css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        /* Dark Background with Starfield Effect */
        body {
            background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        /* Starfield Background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                radial-gradient(2px 2px at 20px 30px, #fff, transparent),
                radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
                radial-gradient(1px 1px at 90px 40px, #fff, transparent),
                radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
                radial-gradient(2px 2px at 160px 30px, #fff, transparent);
            background-repeat: repeat;
            background-size: 200px 100px;
            animation: sparkle 3s linear infinite;
            opacity: 0.3;
            z-index: -1;
        }

        @keyframes sparkle {
            0% { transform: translateY(0px); }
            100% { transform: translateY(-100px); }
        }

        .page-header {
            text-align: center;
            margin: 120px 0 50px;
            color: white;
        }

        .page-header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
            background: linear-gradient(45deg, #FFD700, #FFA500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: textGlow 2s ease-in-out infinite alternate;
        }

        @keyframes textGlow {
            from {
                text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
            }
            to {
                text-shadow: 0 0 30px rgba(255, 215, 0, 0.8);
            }
        }

        .page-header p {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.3rem;
            max-width: 700px;
            margin: 0 auto;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }
        
        .classes-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            padding: 20px 0 50px;
            max-width: 1200px;
            margin: 0 auto;
        }

        /* Course Categories */
        .course-category {
            margin-bottom: 60px;
        }

        .category-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .category-title {
            font-size: 2.2rem;
            font-weight: 700;
            margin-bottom: 15px;
            background: linear-gradient(45deg, #FFD700, #FFA500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
        }

        .category-subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.1rem;
        }

        .category-classes {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
            gap: 25px;
            justify-items: center;
        }

        .class-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 500px;
            transition: all 0.4s ease;
            position: relative;
            color: white;
            display: flex;
            align-items: center;
            min-height: 160px;
        }

        .class-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.05), transparent);
            transform: translateX(-100%);
            transition: transform 0.6s ease;
        }

        .class-card:hover::before {
            transform: translateX(100%);
        }

        .class-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            background: rgba(255, 255, 255, 0.08);
            border-color: rgba(255, 215, 0, 0.3);
        }
        
        .class-image {
            width: 120px;
            height: 90px;
            flex-shrink: 0;
            overflow: hidden;
            border-radius: 10px;
            margin: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        .class-image img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }
        
        .class-details {
            flex: 1;
            padding: 15px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .class-name {
            font-size: 1.3rem;
            color: white;
            margin-bottom: 10px;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        }

        .class-schedule {
            margin-bottom: 12px;
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .schedule-item {
            display: flex;
            align-items: center;
            color: rgba(255, 255, 255, 0.9);
            background: rgba(255, 255, 255, 0.05);
            padding: 4px 8px;
            border-radius: 6px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            flex: 1;
            min-width: 100px;
            font-size: 0.85rem;
        }

        .schedule-item i {
            color: #FFD700;
            margin-right: 6px;
            font-size: 0.9rem;
        }

        .students-section {
            margin-top: 15px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding-top: 12px;
        }

        .students-count {
            font-weight: 600;
            color: white;
            margin-bottom: 8px;
            font-size: 0.9rem;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        }

        .students-avatars {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
            max-height: 60px;
            overflow-y: auto;
        }

        .student-avatar {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            overflow: hidden;
            border: 2px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            flex-shrink: 0;
        }

        .student-avatar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 50%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            animation: avatarGlow 2s ease-in-out infinite;
        }

        @keyframes avatarGlow {
            0%, 100% { opacity: 0; }
            50% { opacity: 1; }
        }

        .student-avatar:hover {
            transform: scale(1.2);
            box-shadow: 0 4px 10px rgba(255, 215, 0, 0.4);
            border-color: #FFD700;
            z-index: 10;
        }

        .student-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .empty-class {
            background: rgba(255, 255, 255, 0.02);
            border: 2px dashed rgba(255, 255, 255, 0.2);
        }

        .empty-notice {
            color: rgba(255, 255, 255, 0.6);
            font-style: italic;
            margin-top: 8px;
        }
        
        .join-class {
            display: inline-block;
            background: linear-gradient(45deg, #FFD700, #FFA500);
            color: #333;
            padding: 8px 16px;
            border-radius: 20px;
            text-decoration: none;
            margin-top: 12px;
            font-weight: 600;
            font-size: 0.85rem;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(255, 215, 0, 0.3);
            text-shadow: none;
        }

        .join-class:hover {
            background: linear-gradient(45deg, #FFA500, #FFD700);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
        }

        .join-class.disabled {
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.5);
            cursor: not-allowed;
            opacity: 0.6;
            box-shadow: none;
        }

        .join-class.disabled:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: none;
            box-shadow: none;
        }

        .access-denied {
            background: rgba(244, 67, 54, 0.1);
            color: #ff6b6b;
            border: 1px solid rgba(244, 67, 54, 0.3);
            border-radius: 10px;
            padding: 12px;
            margin-top: 15px;
            font-size: 0.9rem;
            backdrop-filter: blur(10px);
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: rgba(255, 255, 255, 0.8);
        }

        .error-message {
            background: rgba(244, 67, 54, 0.1);
            color: #ff6b6b;
            border: 1px solid rgba(244, 67, 54, 0.3);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            backdrop-filter: blur(10px);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .category-classes {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .class-card {
                flex-direction: column;
                text-align: center;
                min-height: auto;
                max-width: 100%;
            }

            .class-image {
                width: 100px;
                height: 80px;
                margin: 15px auto 0;
            }

            .class-details {
                padding: 15px;
            }

            .class-schedule {
                flex-direction: column;
                gap: 8px;
            }

            .schedule-item {
                justify-content: center;
                min-width: auto;
            }

            .students-avatars {
                justify-content: center;
                max-height: 80px;
            }

            .page-header h1 {
                font-size: 2.5rem;
            }

            .category-title {
                font-size: 1.8rem;
            }
        }

        @media (max-width: 480px) {
            .category-classes {
                grid-template-columns: 1fr;
                gap: 12px;
            }

            .class-image {
                width: 80px;
                height: 60px;
            }

            .page-header h1 {
                font-size: 2rem;
            }

            .category-title {
                font-size: 1.5rem;
            }

            .class-name {
                font-size: 1.2rem;
            }

            .student-avatar {
                width: 24px;
                height: 24px;
            }

            .students-avatars {
                max-height: 50px;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../assets/images/logo.jpg" alt="VTA Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../index.html">Trang Chủ</a></li>
                    <li><a href="index.html" class="active">Lớp Học</a></li>
                    <li><a href="../achievements/">Thành Tích</a></li>
                    <li><a href="../auth/register.html">Đăng Ký</a></li>
                    <li><a href="../rankings/">Bảng Xếp Hạng</a></li>
                    <li><a href="../research/">Nghiên Cứu</a></li>
                    <li><a href="../auth/">Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Classes Section -->
    <section>
        <div class="container">
            <div class="page-header">
                <h1>Các Lớp Học Hiện Tại</h1>
                <p>Khám phá và tham gia các lớp học Python, Scratch và KHKT-STEM cùng Vthon Academy</p>
            </div>

            <!-- Loading State -->
            <div id="loadingState" class="loading">
                <i class="fas fa-spinner fa-spin"></i> Đang tải thông tin lớp học...
            </div>

            <!-- Error Message -->
            <div id="errorMessage" class="error-message" style="display: none;"></div>

            <div id="classesContainer" style="display: none;">
                <!-- Classes will be loaded dynamically by categories -->
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section footer-logo">
                    <div class="footer-logo-container">
                        <img src="../assets/images/logo.jpg" alt="VTA Logo">
                        <div class="logo-text">Vthon Academy</div>
                    </div>
                    <div class="slogan">Học, học nữa, học mãi.</div>
                    <div class="footer-bottom">
                        <p>&copy; 2025 – All rights reserved.</p>
                    </div>
                </div>

                <div class="footer-section footer-contact">
                    <h3>Liên hệ</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> 0399787678</p>
                    <p><i class="fab fa-facebook-messenger"></i> Zalo: 0399787678</p>
                </div>

                <div class="footer-section footer-social">
                    <h3>Mạng xã hội</h3>
                    <div class="social-links">
                        <a href="https://www.facebook.com/vinhle030904/" target="_blank" class="social-link facebook">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </a>
                        <a href="https://www.tiktok.com/@sunnii39" target="_blank" class="social-link tiktok">
                            <i class="fab fa-tiktok"></i> TikTok
                        </a>
                        <a href="https://www.instagram.com/vlee.39/?hl=en" target="_blank" class="social-link instagram">
                            <i class="fab fa-instagram"></i> Instagram
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js";
        import { getAuth, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js";
        import { getFirestore, doc, getDoc, collection, query, where, getDocs } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
            authDomain: "classroom-web-48bc2.firebaseapp.com",
            projectId: "classroom-web-48bc2",
            storageBucket: "classroom-web-48bc2.firebasestorage.app",
            messagingSenderId: "446746787502",
            appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
            measurementId: "G-742XRP9E96"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        // Cache for students data to avoid repeated Firebase calls
        const studentsCache = new Map();

        // UI Elements
        const loadingState = document.getElementById('loadingState');
        const errorMessage = document.getElementById('errorMessage');
        const classesContainer = document.getElementById('classesContainer');

        // Classes data organized by categories
        const classesData = {
            python: {
                title: 'Lập Trình Python - AI',
                subtitle: 'Học Python từ cơ bản đến nâng cao, ứng dụng AI và Machine Learning',
                image: '../assets/images/courses/python-course.jpg',
                classes: [
                    {
                        id: 'python-a',
                        name: 'Python - A',
                        schedule: 'Thứ 7 - Chủ Nhật',
                        time: '19:30 - 21:00',
                        students: 0,
                        maxStudents: 10
                    },
                    {
                        id: 'python-b',
                        name: 'Python - B',
                        schedule: 'Thứ 2 - Thứ 4',
                        time: '19:30 - 21:00',
                        students: 0,
                        maxStudents: 10
                    },
                    {
                        id: 'python-c',
                        name: 'Python - C',
                        schedule: 'Thứ 3 - Thứ 5',
                        time: '19:30 - 21:00',
                        students: 0,
                        maxStudents: 10
                    }
                ]
            },
            scratch: {
                title: 'Scratch - Tin Học Cơ Bản',
                subtitle: 'Lập trình Scratch cho trẻ em, học Excel, Word, Canva',
                image: '../assets/images/courses/scratch-course.jpg',
                classes: [
                    {
                        id: 'scratch-a',
                        name: 'Scratch - A',
                        schedule: 'Thứ 6 - Chủ Nhật',
                        time: '14:00 - 15:30',
                        students: 0,
                        maxStudents: 10
                    }
                ]
            },
            stem: {
                title: 'KHKT - STEM',
                subtitle: 'Hỗ trợ nghiên cứu khoa học kỹ thuật, dự án STEM',
                image: '../assets/images/courses/stem-course.jpg',
                classes: [
                    {
                        id: 'khkt-a',
                        name: 'KHKT - A',
                        schedule: 'Thứ 2 - Thứ 6',
                        time: '20:00 - 21:30',
                        students: 0,
                        maxStudents: 8
                    }
                ]
            }
        };



        // Get all students data at once and cache it
        async function getAllStudentsData() {
            try {
                // Check cache first
                if (studentsCache.size > 0) {
                    return studentsCache;
                }

                const q = query(collection(db, "users"));
                const querySnapshot = await getDocs(q);

                querySnapshot.forEach((doc) => {
                    const userData = doc.data();
                    // Only include users who have completed their profile and have a class
                    if (userData.fullName && userData.fullName.trim() !== '' && userData.courseClass) {
                        const classId = userData.courseClass;

                        if (!studentsCache.has(classId)) {
                            studentsCache.set(classId, []);
                        }

                        studentsCache.get(classId).push({
                            id: doc.id,
                            name: userData.fullName,
                            avatar: userData.avatar || '../assets/images/avatars/avatar_boy_1.png'
                        });
                    }
                });

                return studentsCache;
            } catch (error) {
                console.error("Error getting all students data:", error);
                return new Map();
            }
        }

        // Get students data for a specific class (from cache)
        function getStudentsData(classId) {
            return studentsCache.get(classId) || [];
        }

        // Get students count for a specific class (from cache)
        function getStudentsCount(classId) {
            const students = studentsCache.get(classId) || [];
            return students.length;
        }

        // Check if user has access to a class
        async function checkClassAccess(user, classId) {
            if (!user) return false;

            try {
                const userDoc = await getDoc(doc(db, "users", user.uid));
                if (userDoc.exists()) {
                    const userData = userDoc.data();

                    // Check if user is admin
                    const isAdmin = userData.isAdmin || user.email === '<EMAIL>';

                    // Admin has access to all classes, or user has access to their selected class
                    return isAdmin || userData.courseClass === classId;
                }
                return false;
            } catch (error) {
                console.error("Error checking class access:", error);
                return false;
            }
        }

        // Create class card HTML
        function createClassCard(classData, categoryImage, hasAccess, user) {
            // Get students data from cache
            const studentsData = getStudentsData(classData.id);
            const studentsCount = getStudentsCount(classData.id);

            const isEmpty = studentsCount === 0;
            const cardClass = isEmpty ? 'class-card empty-class' : 'class-card';

            let studentsHTML = '';
            if (studentsCount > 0 && studentsData.length > 0) {
                studentsHTML = '<div class="students-avatars">';
                studentsData.forEach(student => {
                    studentsHTML += `
                        <div class="student-avatar" title="${student.name}" onclick="viewStudentProfile('${student.userId}')" style="cursor: pointer;">
                            <img src="${student.avatar}" alt="${student.name}">
                        </div>
                    `;
                });
                studentsHTML += '</div>';
            } else {
                studentsHTML = '<p class="empty-notice">Lớp học chưa có học viên</p>';
            }

            let actionHTML = '';
            if (!user) {
                actionHTML = `<a href="../auth/" class="join-class">Đăng nhập để tham gia lớp học</a>`;
            } else if (hasAccess) {
                // Check if class file exists for new classes
                const classFileExists = ['python-a', 'python-b', 'python-c', 'scratch-a', 'khkt-a'].includes(classData.id);
                if (classFileExists) {
                    actionHTML = `<a href="${classData.id}.html" class="join-class">Vào lớp học</a>`;
                } else {
                    actionHTML = `<a href="#" class="join-class disabled" onclick="showComingSoon(); return false;">Sắp ra mắt</a>`;
                }
            } else {
                actionHTML = `
                    <a href="#" class="join-class disabled" onclick="showAccessDenied(); return false;">Tham gia lớp học</a>
                    <div class="access-denied">
                        <i class="fas fa-lock"></i>
                        Bạn hiện không có quyền truy cập lớp học này!
                    </div>
                `;
            }

            return `
                <div class="${cardClass}">
                    <div class="class-image">
                        <img src="${categoryImage}" alt="${classData.name}">
                    </div>
                    <div class="class-details">
                        <h3 class="class-name">${classData.name}</h3>
                        <div class="class-schedule">
                            <div class="schedule-item">
                                <i class="fas fa-calendar-alt"></i>
                                <span>${classData.schedule}</span>
                            </div>
                            <div class="schedule-item">
                                <i class="fas fa-clock"></i>
                                <span>${classData.time}</span>
                            </div>
                        </div>
                        <div class="students-section">
                            <div class="students-count">Số học viên: ${studentsCount}/${classData.maxStudents}</div>
                            ${studentsHTML}
                        </div>
                        ${actionHTML}
                    </div>
                </div>
            `;
        }

        // Show access denied message
        window.showAccessDenied = function() {
            alert('Bạn hiện không có quyền truy cập lớp học này!\n\nVui lòng liên hệ giáo viên để được phân lớp hoặc kiểm tra lại thông tin lớp học đã chọn trong tài khoản.');
        };

        // Show coming soon message
        window.showComingSoon = function() {
            alert('Lớp học này sắp ra mắt!\n\nVui lòng theo dõi thông báo từ giáo viên để biết thêm chi tiết.');
        };

        // Load classes
        async function loadClasses(user) {
            try {
                loadingState.style.display = 'block';
                errorMessage.style.display = 'none';
                classesContainer.style.display = 'none';

                // Load all students data once at the beginning
                await getAllStudentsData();

                let classesHTML = '';

                // Loop through each category
                for (const [categoryKey, categoryData] of Object.entries(classesData)) {
                    classesHTML += `
                        <div class="course-category">
                            <div class="category-header">
                                <h2 class="category-title">${categoryData.title}</h2>
                                <p class="category-subtitle">${categoryData.subtitle}</p>
                            </div>
                            <div class="category-classes">
                    `;

                    // Loop through classes in this category
                    for (const classData of categoryData.classes) {
                        const hasAccess = user ? await checkClassAccess(user, classData.id) : false;
                        classesHTML += createClassCard(classData, categoryData.image, hasAccess, user);
                    }

                    classesHTML += `
                            </div>
                        </div>
                    `;
                }

                classesContainer.innerHTML = classesHTML;

                loadingState.style.display = 'none';
                classesContainer.style.display = 'block';
            } catch (error) {
                console.error("Error loading classes:", error);
                loadingState.style.display = 'none';
                errorMessage.textContent = 'Lỗi khi tải thông tin lớp học: ' + error.message;
                errorMessage.style.display = 'block';
            }
        }

        let authStateChecked = false;

        // View student profile (make it global)
        window.viewStudentProfile = function(userId) {
            // Redirect to student profile page with userId parameter
            window.location.href = `../auth/student-profile.html?userId=${userId}`;
        };

        // Auth state change listener
        onAuthStateChanged(auth, (user) => {
            if (!authStateChecked) {
                authStateChecked = true;
                loadClasses(user);
            } else {
                // Only reload if auth state actually changed
                loadClasses(user);
            }
        });
    </script>

    <script src="../assets/js/script.js"></script>
</body>
</html>
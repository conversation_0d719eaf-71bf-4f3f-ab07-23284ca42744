<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scratch - B | Vthon Academy</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: white;
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        /* Shooting stars animation */
        .shooting-star {
            position: absolute;
            width: 2px;
            height: 2px;
            background: #FFD700;
            border-radius: 50%;
            box-shadow: 0 0 10px #FFD700;
            animation: shoot 3s linear infinite;
        }

        @keyframes shoot {
            0% {
                transform: translateX(-100px) translateY(100px);
                opacity: 1;
            }
            100% {
                transform: translateX(100vw) translateY(-100vh);
                opacity: 0;
            }
        }

        /* Floating particles */
        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 215, 0, 0.6);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .class-header {
            background: transparent;
            color: white;
            padding: 60px 0;
            text-align: center;
            margin-top: 80px;
            position: relative;
            z-index: 1;
        }

        .class-header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            word-wrap: break-word;
        }

        .class-header p {
            font-size: 1.1rem;
            opacity: 0.9;
            word-wrap: break-word;
        }

        .navbar {
            background: rgba(26, 26, 46, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 0;
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
            border-bottom: 1px solid rgba(255, 215, 0, 0.2);
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
        }

        .logo {
            display: flex;
            align-items: center;
            font-size: 1.5rem;
            font-weight: 700;
            color: #FFD700;
            text-decoration: none;
        }

        .logo i {
            margin-right: 0.5rem;
            font-size: 1.8rem;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
            align-items: center;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
            padding: 0.5rem 1rem;
            border-radius: 8px;
        }

        .nav-links a:hover {
            color: #FFD700;
            background: rgba(255, 215, 0, 0.1);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .class-info {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 215, 0, 0.2);
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem auto;
            max-width: 800px;
            text-align: center;
        }

        .class-info h2 {
            color: #FFD700;
            margin-bottom: 1rem;
            font-size: 1.5rem;
        }

        .class-info p {
            line-height: 1.6;
            margin-bottom: 1rem;
        }

        .class-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 3rem 0;
        }

        .class-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 215, 0, 0.2);
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .class-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(255, 215, 0, 0.2);
        }

        .class-card i {
            font-size: 3rem;
            color: #FFD700;
            margin-bottom: 1rem;
        }

        .class-card h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: #FFD700;
        }

        .class-card p {
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        .btn {
            background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
            color: #1a1a2e;
            padding: 0.8rem 2rem;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
        }

        .lessons-section {
            margin: 3rem 0;
        }

        .lessons-section h2 {
            text-align: center;
            color: #FFD700;
            margin-bottom: 2rem;
            font-size: 2rem;
        }

        .lesson-item {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 215, 0, 0.2);
            border-radius: 15px;
            margin-bottom: 1rem;
            overflow: hidden;
        }

        .lesson-header {
            padding: 1.5rem;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background 0.3s ease;
        }

        .lesson-header:hover {
            background: rgba(255, 215, 0, 0.1);
        }

        .lesson-title {
            font-weight: 600;
            color: #FFD700;
        }

        .lesson-content {
            padding: 0 1.5rem 1.5rem;
            display: none;
        }

        .lesson-content.active {
            display: block;
        }

        .lesson-content p {
            line-height: 1.6;
            margin-bottom: 1rem;
        }

        .lesson-content ul {
            margin-left: 1.5rem;
            margin-bottom: 1rem;
        }

        .lesson-content li {
            margin-bottom: 0.5rem;
        }

        @media (max-width: 768px) {
            .nav-container {
                padding: 0 1rem;
            }
            
            .nav-links {
                gap: 1rem;
            }
            
            .class-header h1 {
                font-size: 2rem;
            }
            
            .container {
                padding: 0 1rem;
            }
            
            .class-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Shooting stars -->
    <div class="shooting-star" style="top: 20%; animation-delay: 0s;"></div>
    <div class="shooting-star" style="top: 40%; animation-delay: 2s;"></div>
    <div class="shooting-star" style="top: 60%; animation-delay: 4s;"></div>
    <div class="shooting-star" style="top: 80%; animation-delay: 6s;"></div>

    <!-- Floating particles -->
    <div class="particle" style="top: 10%; left: 10%; animation-delay: 0s;"></div>
    <div class="particle" style="top: 20%; left: 80%; animation-delay: 1s;"></div>
    <div class="particle" style="top: 70%; left: 20%; animation-delay: 2s;"></div>
    <div class="particle" style="top: 80%; left: 70%; animation-delay: 3s;"></div>

    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <a href="../index.html" class="logo">
                <i class="fas fa-graduation-cap"></i>
                VTA
            </a>
            <ul class="nav-links">
                <li><a href="../index.html">Trang Chủ</a></li>
                <li><a href="../auth/index.html">Log In</a></li>
                <li><a href="../ranking.html">Bảng Xếp Hạng</a></li>
                <li><a href="../achievements.html">Thành Tích</a></li>
                <li><a href="../index.html#courses">Khóa Học</a></li>
                <li><a href="../index.html#contact">Liên Hệ</a></li>
            </ul>
        </div>
    </nav>

    <!-- Class Header -->
    <section class="class-header">
        <div class="container">
            <h1><i class="fas fa-puzzle-piece"></i> Scratch - B</h1>
            <p>Lớp học Scratch nâng cao - T7 CN 21h00 đến 22h00</p>
        </div>
    </section>

    <!-- Access Control Message -->
    <section class="class-info">
        <div class="container">
            <h2><i class="fas fa-info-circle"></i> Chào mừng đến với lớp Scratch - B!</h2>
            <p>Đây là lớp học Scratch nâng cao dành cho các em đã có kiến thức cơ bản về lập trình.</p>
            <p>Vui lòng đăng nhập để truy cập nội dung lớp học và tham gia các hoạt động.</p>
        </div>
    </section>

    <!-- Class Features -->
    <section class="container">
        <div class="class-grid">
            <div class="class-card">
                <i class="fas fa-calendar-alt"></i>
                <h3>Lịch Học</h3>
                <p>Thứ 7 & Chủ Nhật</p>
                <p>21:00 - 22:00</p>
            </div>
            <div class="class-card">
                <i class="fas fa-users"></i>
                <h3>Học Viên</h3>
                <p>7 học viên</p>
                <p>Vui lòng đăng nhập để xem danh sách</p>
            </div>
            <div class="class-card">
                <i class="fas fa-video"></i>
                <h3>Google Meet</h3>
                <p>Link tham gia lớp học online</p>
                <a href="#" class="btn">Tham Gia Ngay</a>
            </div>
        </div>
    </section>

    <script>
        // Lesson toggle functionality
        document.querySelectorAll('.lesson-header').forEach(header => {
            header.addEventListener('click', () => {
                const content = header.nextElementSibling;
                const icon = header.querySelector('i');
                
                content.classList.toggle('active');
                icon.classList.toggle('fa-chevron-down');
                icon.classList.toggle('fa-chevron-up');
            });
        });

        // Create more shooting stars dynamically
        function createShootingStar() {
            const star = document.createElement('div');
            star.className = 'shooting-star';
            star.style.top = Math.random() * 100 + '%';
            star.style.animationDelay = Math.random() * 3 + 's';
            document.body.appendChild(star);
            
            setTimeout(() => {
                star.remove();
            }, 3000);
        }

        // Create shooting stars periodically
        setInterval(createShootingStar, 2000);

        // Create floating particles
        function createParticle() {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.top = Math.random() * 100 + '%';
            particle.style.left = Math.random() * 100 + '%';
            particle.style.animationDelay = Math.random() * 6 + 's';
            document.body.appendChild(particle);
            
            setTimeout(() => {
                particle.remove();
            }, 6000);
        }

        // Create particles periodically
        setInterval(createParticle, 1000);
    </script>
</body>
</html>
